<?php
$is_live_learning = $webinar->livestreams["is_live_learning"] ?? 0;
?>

    <!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $webinar->title }} - Webinar</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #4361ee;
            --primary-gradient: linear-gradient(135deg, #4361ee, #3a0ca3);
            --secondary-color: #f72585;
            --accent-color: #4cc9f0;
            --text-color: #2b2d42;
            --light-text: #6c757d;
            --bg-color: #f5f7ff;
            --card-bg: #ffffff;
            --success-color: #06d6a0;
            --warning-color: #ffd166;
            --border-radius: 14px;
            --box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
            --transition: all 0.3s ease;
        }

        body {
            background-color: var(--bg-color);
            font-family: 'Inter', sans-serif;
            color: var(--text-color);
            line-height: 1.6;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%234361ee' fill-opacity='0.03'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3Cpath d='M6 5V0H5v5H0v1h5v94h1V6h94V5H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        .webinar-container {
            max-width: 960px;
            margin: 3rem auto;
        }

        .webinar-card {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--box-shadow);
            transition: var(--transition);
            margin-bottom: 2rem;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .header {
            background: var(--primary-gradient);
            color: white;
            padding: 3rem 2rem;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cG9seWxpbmUgcG9pbnRzPSIxMDAsNTAgNTAsODAgMjEsNjUgMSwzMCIgc3R5bGU9ImZpbGw6bm9uZTtzdHJva2U6cmdiYSgyNTUsMjU1LDI1NSwwLjEpO3N0cm9rZS13aWR0aDoxLjUiIC8+PC9zdmc+') no-repeat center center,
            url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIj48ZmlsdGVyIGlkPSJhIiB4PSIwIiB5PSIwIj48ZmVUdXJidWxlbmNlIGJhc2VGcmVxdWVuY3k9Ii43NSIgc3RpdGNoVGlsZXM9InN0aXRjaCIgdHlwZT0iZnJhY3RhbE5vaXNlIi8+PGZlQ29sb3JNYXRyaXggdmFsdWVzPSIwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwLjMgMCIvPjwvZmlsdGVyPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbHRlcj0idXJsKCNhKSIgb3BhY2l0eT0iMC4xIi8+PC9zdmc+');
            background-size: cover;
            opacity: 0.25;
            z-index: 1;
        }

        .webinar-content {
            position: relative;
            z-index: 2;
        }

        .header h1 {
            font-weight: 700;
            font-size: 2.75rem;
            margin-bottom: 0.5rem;
            letter-spacing: -0.5px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.2rem;
            margin-bottom: 0;
        }

        .speaker-title {
            font-weight: 500;
            color: rgba(255, 255, 255, 0.85);
            text-transform: uppercase;
            font-size: 0.9rem;
            letter-spacing: 0.5px;
            position: relative;
            display: inline-block;
            padding-right: 8px;
        }

        .speaker-name {
            font-weight: 700;
            font-size: 1.3rem;
            color: white;
            position: relative;
        }

        .speaker-name::before {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 100%;
            height: 2px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
        }

        .main-content {
            padding: 2.5rem;
        }

        .speaker-info {
            display: flex;
            align-items: center;
            margin-bottom: 2rem;
            background-color: rgba(67, 97, 238, 0.03);
            padding: 1.25rem;
            border-radius: 12px;
            border-left: 4px solid var(--primary-color);
        }

        .speaker-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: var(--primary-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1.5rem;
            color: white;
            font-size: 2rem;
            box-shadow: 0 8px 20px rgba(67, 97, 238, 0.25);
        }

        .speaker-info h5 {
            font-weight: 600;
            font-size: 1.25rem;
            margin-bottom: 0.3rem;
        }

        .viewers-count {
            display: flex;
            align-items: center;
            font-size: 1rem;
            color: var(--light-text);
            margin-bottom: 2rem;
            padding: 1rem 1.25rem;
            background-color: rgba(0, 0, 0, 0.02);
            border-radius: 12px;
            border: 1px solid rgba(0, 0, 0, 0.03);
        }

        .viewers-count i {
            color: var(--primary-color);
            margin-right: 0.75rem;
            font-size: 1.2rem;
        }

        #viewerCount {
            font-weight: 600;
            color: var(--text-color);
        }

        .countdown-container {
            margin: 2.5rem 0;
            text-align: center;
            background-color: rgba(67, 97, 238, 0.03);
            padding: 2rem;
            border-radius: 14px;
            border: 1px dashed rgba(67, 97, 238, 0.2);
        }

        .countdown-container h4 {
            margin-bottom: 1.5rem;
            font-weight: 600;
            color: var(--primary-dark);
            position: relative;
            display: inline-block;
        }

        .countdown-container h4::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 3px;
            background: var(--primary-gradient);
            border-radius: 10px;
        }

        .countdown {
            display: flex;
            justify-content: center;
            gap: 1.25rem;
        }

        .countdown-item {
            background-color: white;
            border-radius: 12px;
            padding: 1rem 1.5rem;
            min-width: 90px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(0, 0, 0, 0.03);
            position: relative;
            overflow: hidden;
        }

        .countdown-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
            border-radius: 4px 4px 0 0;
        }

        .countdown-value {
            font-size: 2.25rem;
            font-weight: 700;
            color: var(--primary-color);
            line-height: 1;
        }

        .countdown-label {
            font-size: 0.8rem;
            font-weight: 500;
            color: var(--light-text);
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-top: 0.5rem;
        }

        .cta-button {
            background: var(--primary-gradient);
            border: none;
            color: white;
            padding: 1rem 2rem;
            font-weight: 600;
            font-size: 1.1rem;
            border-radius: 8px;
            box-shadow: 0 8px 20px rgba(67, 97, 238, 0.25);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: all 0.6s ease;
            z-index: -1;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 25px rgba(67, 97, 238, 0.35);
            color: white;
        }

        .cta-button:hover::before {
            left: 100%;
        }

        .secondary-button {
            background-color: white;
            border: 1px solid #dee2e6;
            color: var(--text-color);
            padding: 1rem 2rem;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-size: 1.1rem;
        }

        .secondary-button:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.05);
        }

        .form-section {
            max-width: 700px;
            margin: 0 auto;
            display: none;
            animation: fadeIn 0.5s ease;
            background: white;
            border-radius: var(--border-radius);
            padding: 2.5rem;
            box-shadow: var(--box-shadow);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .form-section.show-direct {
            display: block;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .form-section.active {
            display: block;
        }

        .form-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .form-header .icon-wrapper {
            display: inline-block;
            width: 80px;
            height: 80px;
            background: var(--primary-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
            box-shadow: 0 8px 25px rgba(67, 97, 238, 0.25);
        }

        .form-header .icon-wrapper i {
            color: white;
            font-size: 2rem;
        }

        .form-header h3 {
            font-weight: 700;
            color: var(--text-color);
            margin-bottom: 0.5rem;
            font-size: 1.75rem;
        }

        .form-header p {
            color: var(--light-text);
            font-size: 1.1rem;
            margin-bottom: 0;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .form-grid .input-group {
            margin-bottom: 0;
        }

        .form-grid .input-group.full-width {
            grid-column: 1 / -1;
        }

        .webinar-description {
            background-color: rgba(67, 97, 238, 0.03);
            border-radius: 12px;
            padding: 1.75rem;
            margin-bottom: 2.5rem;
            border-left: 4px solid var(--primary-color);
            font-size: 1.05rem;
        }

        .webinar-description p:last-child {
            margin-bottom: 0;
        }

        .input-group {
            margin-bottom: 1.75rem;
        }

        .input-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--text-color);
        }

        .input-group input {
            width: 100%;
            padding: 0.9rem 1.2rem;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 1rem;
            transition: var(--transition);
            background-color: #f8f9fa;
        }

        .input-group input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
            outline: none;
            background-color: white;
        }

        .required::after {
            content: " *";
            color: var(--secondary-color);
        }

        .note {
            font-size: 0.85rem;
            color: var(--light-text);
            margin-top: 1.5rem;
            border-radius: 8px;
        }

        .live-badge {
            display: inline-block;
            padding: 0.4rem 1rem;
            background: linear-gradient(to right, #f72585, #ff4d6d);
            color: white;
            font-size: 0.85rem;
            font-weight: 600;
            border-radius: 50px;
            margin-bottom: 1.5rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            animation: pulse 2s infinite;
            box-shadow: 0 4px 15px rgba(247, 37, 133, 0.3);
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(247, 37, 133, 0.4);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(247, 37, 133, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(247, 37, 133, 0);
            }
        }

        .viewer-joined {
            animation: slideUp 0.5s ease;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .header {
                padding: 2rem 1.5rem;
            }

            .header h1 {
                font-size: 2rem;
            }

            .main-content {
                padding: 1.5rem;
            }

            .speaker-avatar {
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
            }

            .countdown-item {
                min-width: 70px;
                padding: 0.75rem;
            }

            .countdown-value {
                font-size: 1.75rem;
            }
        }

        /* WebView Detection Styles */
        .webview-notice {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            text-align: center;
            z-index: 9999;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .webview-notice a {
            color: #0056b3;
            text-decoration: none;
            font-weight: 600;
        }

        .webview-notice a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
<!-- WebView Detection Notice -->
<div id="webview-notice" class="webview-notice">
    <p class="mb-0">Nền tảng webinar không hỗ trợ trên ứng dụng này, Vui lòng <a href="#" id="open-in-browser">click vào
            đây để truy cập</a></p>
</div>

<div class="container webinar-container">
    <div class="webinar-card">
        <div class="header">
            <div class="webinar-content">
                <h1>{{ $webinar->title }}</h1>
                <p>
                    <span class="speaker-title">HOST:</span>
                    <span class="speaker-name">{{ $webinar->speaker }}</span>
                </p>
            </div>
        </div>

        <div class="main-content">
            @if (session('error'))
                <div class="alert alert-danger mb-4">
                    {{ session('error') }}
                </div>
            @endif

            @if($is_live_learning==1)
            <!-- Registration Form (Initially Hidden) -->
                <div id="registration-form" class="form-section" style="display: block!important">
                    <div class="text-center mb-4">
                        <div class="d-inline-block mb-3 p-3 rounded-circle"
                             style="background: rgba(67, 97, 238, 0.05);">
                            <i class="fas fa-user-edit fa-2x text-primary"></i>
                        </div>
                        <h3 class="fw-bold mb-2">Đăng ký tham gia lớp học</h3>
                        <p class="text-muted">Hoàn thành thông tin dưới đây để tham gia lớp học</p>
                    </div>
                    <form action="{{route('join.login.learning',$webinar->join_code)}}" method="POST" class="p-1">
                        @csrf
                        <div class="input-group">
                            <label for="email"
                                   class="required">
                                <i class="fas fa-envelope text-primary me-2 opacity-75"></i>Email
                            </label>
                            <input type="email" id="email" name="email" value="{{ old('email') }}"
                                   placeholder="Nhập địa chỉ email của bạn"
                                   required>
                        </div>
                        <div class="input-group">
                            <label for="password"
                                   class="required">
                                <i class="fas fa-envelope text-primary me-2 opacity-75"></i>Mật khẩu
                            </label>
                            <input type="password" id="password" name="password" placeholder="Nhập mật khẩu"
                                   required>
                        </div>
                        <div class="mt-4">
                            <button type="submit" class="btn cta-button w-100">
                                <i class="fas fa-sign-in-alt me-2"></i> Truy cập lớp học
                            </button>
                        </div>
                    </form>
                </div>
            @else
                <div class="viewers-count" @if(!isset($isLive) || !$isLive) style="display: none;" @endif>
                    <i class="fas fa-users me-2"></i>
                    <span id="viewerCount">{{ $webinar->virtual_viewers }}</span> <span class="ms-1">người đang tham gia webinar này</span>
                </div>

                @if(request()->has('debug'))
                    <div class="stream-duration">
                        <i class="fas fa-broadcast-tower"></i> Đang phát: <span id="stream-duration">00:00:00</span>
                    </div>
                @endif

                <div id="webinar-details">

                    @if(isset($is_live_stream) && $is_live_stream==1)
                        <div class="text-center mb-4">
                            <span class="live-badge"><i class="fas fa-circle me-2"></i>Live</span>
                            <h4 class="fw-bold">Webinar đang phát trực tiếp!</h4>
                            <p class="text-muted">Đăng ký ngay để tham gia buổi webinar</p>
                        </div>


                        <div class="webinar-description">
                            <p>
                                <i class="fas fa-broadcast-tower me-2 text-primary"></i> Webinar đang phát trực
                                tiếp!
                                Đăng ký để tham gia ngay.
                            </p>
                        </div>

                        <div class="text-center mt-4">
                            <button id="registerButton" class="btn cta-button btn-lg">
                                <i class="fas fa-user-plus me-2"></i> Đăng Ký Tham Gia
                            </button>
                        </div>
                    @elseif(isset($webinarEnded) && $webinarEnded && !$webinar->allow_replay)
                        <div class="alert alert-warning text-center mt-4">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Buổi webinar đã kết thúc</strong>
                            <p class="mb-0 mt-2">Buổi webinar này đã diễn ra và hiện tại không được phép xem lại.</p>
                        </div>

                        <div class="text-center mt-4">
                            <a href="{{ url('/') }}" class="btn secondary-button">
                                <i class="fas fa-home me-2"></i> Quay về trang chủ
                            </a>
                        </div>
                    @elseif($webinarActive)
                        @php
                            $now = \Carbon\Carbon::now();
                            // Định nghĩa $scheduledTime từ currentSchedule
                            $scheduledTime = isset($currentSchedule) ?
                                \Carbon\Carbon::parse($currentSchedule['date'] . ' ' . $currentSchedule['time']) :
                                \Carbon\Carbon::now()->addDay(); // Giá trị mặc định

                            // Khởi tạo biến mặc định
                            $countdownTitle = "Đếm ngược:";
                            $buttonText = "Tham gia";
                            $targetTime = null;
                            $showCountdown = false;
                            $isLive = false;

                            if($inWaitingPeriod) {
                                $targetTime = $scheduledTime;
                                $countdownTitle = "Webinar bắt đầu trong:";
                                $buttonText = "Vào phòng chờ";
                                $showCountdown = true;
                            } else if(isset($scheduledTime) && $scheduledTime->isFuture()) {
                                $targetTime = $scheduledTime->copy()->subMinutes($webinar->waiting_time);
                                $countdownTitle = "Phòng chờ mở trong:";
                                $buttonText = "Nhắc nhở tôi";
                                $showCountdown = true;
                            } else {
                                $isLive = true;
                            }
                        @endphp

                        @if($isLive)
                            <div class="text-center mb-4">
                                <span class="live-badge"><i class="fas fa-circle me-2"></i>Live</span>
                                <h4 class="fw-bold">Webinar đang phát trực tiếp!</h4>
                                <p class="text-muted">Đăng ký ngay để tham gia buổi webinar</p>
                            </div>
                        @endif

                        @if($showCountdown)
                            <div class="countdown-container">
                                <h4>{{ $countdownTitle }}</h4>
                                <div class="countdown" id="countdown"
                                     data-target="{{ isset($targetTime) && is_object($targetTime) ? $targetTime->timestamp : '' }}">
                                    <div class="countdown-item">
                                        <div class="countdown-value" id="days">--</div>
                                        <div class="countdown-label">Ngày</div>
                                    </div>
                                    <div class="countdown-item">
                                        <div class="countdown-value" id="hours">--</div>
                                        <div class="countdown-label">Giờ</div>
                                    </div>
                                    <div class="countdown-item">
                                        <div class="countdown-value" id="minutes">--</div>
                                        <div class="countdown-label">Phút</div>
                                    </div>
                                    <div class="countdown-item">
                                        <div class="countdown-value" id="seconds">--</div>
                                        <div class="countdown-label">Giây</div>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <div class="webinar-description">
                            <p>
                                @if($inWaitingPeriod)
                                    <i class="fas fa-info-circle me-2 text-primary"></i> Phòng chờ đã mở! Bạn cần đăng
                                    ký để
                                    tham gia.
                                @elseif(isset($webinarActive) && $webinarActive)
                                    <i class="fas fa-broadcast-tower me-2 text-primary"></i> Webinar đang phát trực
                                    tiếp!
                                    Đăng ký để tham gia ngay.
                                @elseif($webinar->video_path && isset($showRecording) && $showRecording)
                                    <i class="fas fa-play-circle me-2 text-primary"></i> Bản ghi webinar đã sẵn sàng.
                                    Đăng
                                    ký để xem.
                                @else
                                    @if(isset($scheduledTime) && !$scheduledTime->isPast())
                                        <i class="fas fa-calendar-alt me-2 text-primary"></i> Dự kiến diễn ra vào
                                        <strong>{{ $scheduledTime->format('l, F jS, Y') }}</strong> lúc
                                        <strong>{{ $scheduledTime->format('h:i A') }}</strong>
                                    @else
                                        <i class="fas fa-clock me-2 text-primary"></i> Thời gian webinar chưa được thiết
                                        lập
                                    @endif
                                @endif
                            </p>
                        </div>

                        <div class="text-center mt-4">
                            <button id="registerButton" class="btn cta-button btn-lg">
                                <i class="fas fa-user-plus me-2"></i> Đăng Ký Tham Gia
                            </button>
                        </div>
                    @else
                        <div class="alert alert-info mt-4">
                            @if(isset($inWaitingPeriod) && $inWaitingPeriod)

                                <div class="text-center mb-4">
                                    <span class="live-badge"><i class="fas fa-circle me-2"></i>Live</span>
                                    <h4 class="fw-bold">Webinar đang phát trực tiếp!</h4>
                                    <p class="text-muted">Đăng ký ngay để tham gia buổi webinar</p>
                                </div>


                                <div class="webinar-description">
                                    <p>
                                        <i class="fas fa-broadcast-tower me-2 text-primary"></i> Webinar đang phát trực
                                        tiếp!
                                        Đăng ký để tham gia ngay.
                                    </p>
                                </div>

                                <div class="text-center mt-4">
                                    <button id="registerButton" class="btn cta-button btn-lg">
                                        <i class="fas fa-user-plus me-2"></i> Đăng Ký Tham Gia
                                    </button>
                                </div>
                            @elseif($startTimeWebinar)
                                Thời gian phát sóng vào lúc: {{$startTimeWebinar->format("H:i d/m/Y")}}
                                <div class="text-center mt-3">
                                    <button id="registerButton" class="btn cta-button">
                                        <i class="fas fa-user-plus me-2"></i> Đăng Ký Tham Gia
                                    </button>
                                </div>
                            @else
                                <i class="fas fa-info-circle me-2"></i>
                                Không có phiên webinar nào sắp diễn ra.
                                @if($webinar->video_path && isset($showRecording) && $showRecording)
                                    <p class="mt-3 mb-0">Bạn có thể xem bản ghi sau khi đăng ký:</p>
                                    <div class="text-center mt-3">
                                        <button id="registerButton" class="btn cta-button">
                                            <i class="fas fa-user-plus me-2"></i> Đăng Ký Tham Gia
                                        </button>
                                    </div>
                                @else
                                    <p class="mt-3 mb-0">Vui lòng quay lại sau khi có lịch phát hoặc liên hệ với ban tổ
                                        chức
                                        để
                                        biết thêm chi tiết.</p>
                                @endif
                            @endif

                        </div>
                    @endif
                </div>

                <!-- Registration Form (Initially Hidden) -->
                <div id="registration-form" class="form-section">
                    <h3 class="mb-3">Đăng ký tham gia</h3>
                    <p class="text-muted mb-4">Đăng ký để nhận thông báo về các webinar tiếp theo và tài liệu liên
                        quan.</p>

                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form action="/join/{{ $webinar->join_code }}/form" method="POST" id="join-form">
                    @csrf
                    <!-- Debug info -->
                        <input type="hidden" name="debug_info" value="form-submission-{{ time() }}">

                        <!-- UTM Tracking Parameters -->
                        <input type="hidden" name="utm_source" value="{{ request()->get('utm_source') }}">
                        <input type="hidden" name="utm_medium" value="{{ request()->get('utm_medium') }}">
                        <input type="hidden" name="utm_campaign" value="{{ request()->get('utm_campaign') }}">
                        <input type="hidden" name="utm_term" value="{{ request()->get('utm_term') }}">
                        <input type="hidden" name="utm_content" value="{{ request()->get('utm_content') }}">

                        <div class="input-group">
                            <label for="name"
                                   class="@if(isset($webinar->join_settings['name_required']) && $webinar->join_settings['name_required']) required @endif">
                                Họ và tên
                            </label>
                            <input type="text" id="name" name="name" value="{{ old('name') }}"
                                   @if(isset($webinar->join_settings['name_required']) && $webinar->join_settings['name_required']) required @endif>
                        </div>

                        <div class="input-group">
                            <label for="phone"
                                   class="@if(isset($webinar->join_settings['phone_required']) && $webinar->join_settings['phone_required']) required @endif">
                                Số điện thoại
                            </label>
                            <input type="tel" id="phone" name="phone" value="{{ old('phone') }}"
                                   @if(isset($webinar->join_settings['phone_required']) && $webinar->join_settings['phone_required']) required @endif>
                        </div>

                        <div class="input-group">
                            <label for="email"
                                   class="@if(isset($webinar->join_settings['email_required']) && $webinar->join_settings['email_required']) required @endif">
                                Email
                            </label>
                            <input type="email" id="email" name="email" value="{{ old('email') }}"
                                   @if(isset($webinar->join_settings['email_required']) && $webinar->join_settings['email_required']) required @endif>
                        </div>

                        <div class="note">
                            <span class="text-danger">*</span> Trường bắt buộc
                        </div>

                        <div class="mt-4">
                            @if(setting('recaptcha_enabled')==="1")
                                <button class="btn cta-button w-100 g-recaptcha"
                                        data-sitekey="{{ setting('recaptcha_site_key') }}" data-callback='onJoinSubmit'
                                        data-action='submit'>
                                    <i class="fas fa-sign-in-alt me-2"></i> Tham gia ngay
                                </button>
                            @else
                                <button type="submit" class="btn cta-button w-100">
                                    <i class="fas fa-sign-in-alt me-2"></i> Tham gia ngay
                                </button>
                            @endif
                        </div>
                    </form>
                </div>

                <!-- Registration Form (Initially Hidden) -->
                <div id="registration-form" class="form-section">
                    <div class="text-center mb-4">
                        <div class="d-inline-block mb-3 p-3 rounded-circle"
                             style="background: rgba(67, 97, 238, 0.05);">
                            <i class="fas fa-user-edit fa-2x text-primary"></i>
                        </div>
                        <h3 class="fw-bold mb-2">Đăng ký tham gia</h3>
                        <p class="text-muted">Hoàn thành thông tin dưới đây để tham gia webinar</p>
                    </div>

                    @if ($errors->any())
                        <div class="alert alert-danger shadow-sm border-0">
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form action="/join/{{ $webinar->join_code }}/form" method="POST" class="p-1">
                        @csrf
                        <input type="hidden" name="debug_info" value="form-submission-{{ time() }}">

                        <!-- UTM Tracking Parameters -->
                        <input type="hidden" name="utm_source" value="{{ request()->get('utm_source') }}">
                        <input type="hidden" name="utm_medium" value="{{ request()->get('utm_medium') }}">
                        <input type="hidden" name="utm_campaign" value="{{ request()->get('utm_campaign') }}">
                        <input type="hidden" name="utm_term" value="{{ request()->get('utm_term') }}">
                        <input type="hidden" name="utm_content" value="{{ request()->get('utm_content') }}">

                        <div class="input-group">
                            <label for="name"
                                   class="@if(isset($webinar->join_settings['name_required']) && $webinar->join_settings['name_required']) required @endif">
                                <i class="fas fa-user text-primary me-2 opacity-75"></i>Họ và tên
                            </label>
                            <input type="text" id="name" name="name" value="{{ old('name') }}"
                                   placeholder="Nhập họ và tên của bạn"
                                   @if(isset($webinar->join_settings['name_required']) && $webinar->join_settings['name_required']) required @endif>
                        </div>

                        <div class="input-group">
                            <label for="phone"
                                   class="@if(isset($webinar->join_settings['phone_required']) && $webinar->join_settings['phone_required']) required @endif">
                                <i class="fas fa-phone-alt text-primary me-2 opacity-75"></i>Số điện thoại
                            </label>
                            <input type="tel" id="phone" name="phone" value="{{ old('phone') }}"
                                   placeholder="Nhập số điện thoại của bạn"
                                   @if(isset($webinar->join_settings['phone_required']) && $webinar->join_settings['phone_required']) required @endif>
                        </div>

                        <div class="input-group">
                            <label for="email"
                                   class="@if(isset($webinar->join_settings['email_required']) && $webinar->join_settings['email_required']) required @endif">
                                <i class="fas fa-envelope text-primary me-2 opacity-75"></i>Email
                            </label>
                            <input type="email" id="email" name="email" value="{{ old('email') }}"
                                   placeholder="Nhập địa chỉ email của bạn"
                                   @if(isset($webinar->join_settings['email_required']) && $webinar->join_settings['email_required']) required @endif>
                        </div>

                        <div class="note">
                            <i class="fas fa-info-circle me-2"></i>
                            <span class="text-danger">*</span> Trường bắt buộc phải nhập
                        </div>

                        <div class="d-flex gap-3 mt-4">
                            <button type="button" id="backButton" class="btn secondary-button">
                                <i class="fas fa-arrow-left me-2"></i> Quay lại
                            </button>
                            <button type="submit" class="btn cta-button flex-grow-1">
                                <i class="fas fa-sign-in-alt me-2"></i> Tham gia ngay
                            </button>
                        </div>

                        <div class="mt-4">
                            <button type="submit" class="btn cta-button w-100">
                                <i class="fas fa-sign-in-alt me-2"></i> Tham gia ngay
                            </button>
                        </div>
                    </form>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="https://www.google.com/recaptcha/api.js"></script>
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script>
    function onJoinSubmit(token) {
        document.getElementById("join-form").submit();
    }
</script>
<script>
    $(document).ready(function () {
        // Toggle between webinar details and registration form
        const $registerButton = $('#registerButton');
        const $backButton = $('#backButton');
        const $webinarDetails = $('#webinar-details');
        const $registrationForm = $('#registration-form');

        if ($registerButton.length) {
            $registerButton.on('click', function () {
                $webinarDetails.hide();
                $registrationForm.addClass('active');
            });
        }

        if ($backButton.length) {
            $backButton.on('click', function () {
                $webinarDetails.show();
                $registrationForm.removeClass('active');
            });
        }

        // Virtual viewer count animation with more realistic behavior
        const $viewerCount = $('#viewerCount');
        if ($viewerCount.length) {
            let viewerValue = $viewerCount.text();
            let minCount, maxCount, currentCount;

            // Add a small delay before showing viewer count to new visitors
            setTimeout(() => {
                // Parse the viewer count
                if (viewerValue.includes('-')) {
                    const range = viewerValue.split('-');
                    minCount = parseInt(range[0]);
                    maxCount = parseInt(range[1]);
                    // Generate a random count within the range
                    currentCount = Math.floor(Math.random() * (maxCount - minCount + 1)) + minCount;
                } else {
                    currentCount = parseInt(viewerValue);
                }

                // Show initial count
                $viewerCount.text(currentCount);

                // Simulate viewers joining and leaving
                setInterval(() => {
                    // Get current timestamp to create more organic-looking changes
                    const now = new Date().getTime();

                    // More viewers tend to join in the first few minutes
                    // so weight changes toward positive early on
                    const timeOnPage = (now % 300000) / 300000; // 0-1 based on 5 minute cycle

                    // Generate a weighted random change (-2 to +4)
                    let changeOptions;

                    if (timeOnPage < 0.2) {
                        // First minute: higher chance of viewers joining
                        changeOptions = [-1, 0, 1, 1, 2, 2, 3];
                    } else if (timeOnPage > 0.8) {
                        // Last minute: higher chance of viewers leaving
                        changeOptions = [-2, -1, -1, 0, 0, 1];
                    } else {
                        // Middle period: balanced changes
                        changeOptions = [-1, -1, 0, 0, 0, 1, 1];
                    }

                    // Select a random change from our weighted options
                    const change = changeOptions[Math.floor(Math.random() * changeOptions.length)];

                    // Apply the change with constraints
                    if (viewerValue.includes('-')) {
                        // Stay within minimum and maximum range
                        currentCount = Math.max(minCount, Math.min(maxCount, currentCount + change));
                    } else {
                        // Never go below 1 viewer
                        currentCount = Math.max(1, currentCount + change);
                    }

                    // Occasionally show "someone joined" message for user engagement
                    if (change > 0 && Math.random() < 0.3) {
                        const $viewerCountElement = $('.viewers-count');

                        // Create a temporary notification that fades out
                        const $notification = $('<div>', {
                            class: 'viewer-joined',
                            html: '<i class="fas fa-user-plus text-success me-2"></i> Người xem mới vừa tham gia',
                            css: {
                                position: 'absolute',
                                top: '105%',
                                left: 0,
                                fontSize: '0.8rem',
                                opacity: 0,
                                transition: 'opacity 0.5s ease'
                            }
                        });

                        $viewerCountElement.css('position', 'relative').append($notification);

                        // Show and then hide the notification
                        setTimeout(() => {
                            $notification.css('opacity', '1');
                            setTimeout(() => {
                                $notification.css('opacity', '0');
                                setTimeout(() => {
                                    $notification.remove();
                                }, 500);
                            }, 2000);
                        }, 10);
                    }

                    // Update the display
                    $viewerCount.text(currentCount);
                }, 8000 + (Math.random() * 4000)); // Randomize interval 8-12 seconds
            }, 1500); // Initial delay
        }

        // Countdown timer
        const $countdown = $('#countdown');
        if ($countdown.length && $countdown.data('target')) {
            const targetTime = parseInt($countdown.data('target'));

            function updateCountdown() {
                const now = Math.floor(Date.now() / 1000);
                const diff = targetTime - now;

                if (diff <= 0) {
                    // Time's up
                    $('#days, #hours, #minutes, #seconds').text('0');

                    // Reload page to update status
                    location.reload();
                    return;
                }

                const days = Math.floor(diff / 86400);
                const hours = Math.floor((diff % 86400) / 3600);
                const minutes = Math.floor((diff % 3600) / 60);
                const seconds = Math.floor(diff % 60);

                $('#days').text(days);
                $('#hours').text(hours);
                $('#minutes').text(minutes);
                $('#seconds').text(seconds);
            }

            updateCountdown();
            setInterval(updateCountdown, 1000);
        }

        // Reminder button
        const $remindButton = $('#remindButton');
        if ($remindButton.length) {
            $remindButton.on('click', function () {
                if (Notification.permission !== 'granted') {
                    Notification.requestPermission().then(function (permission) {
                        if (permission === 'granted') {
                            setReminder();
                        }
                    });
                } else {
                    setReminder();
                }
            });
        }

        function setReminder() {
            $remindButton
                .html('<i class="fas fa-check me-2"></i> Đã đặt nhắc nhở')
                .removeClass('btn-outline-primary')
                .addClass('btn-success')
                .prop('disabled', true);

            Swal.fire({
                icon: 'success',
                title: 'Đã đặt nhắc nhở',
                text: 'Bạn sẽ được thông báo khi phòng chờ mở.',
                timer: 3000,
                timerProgressBar: true
            });
        }
    });
</script>

@include('sweetalert::alert')

<script>
    // WebView Detection Functions
    function isWebView() {
        const userAgent = navigator.userAgent.toLowerCase();

        // Check for common browser indicators
        const isChrome = userAgent.includes('chrome') && !userAgent.includes('edg');
        const isSafari = userAgent.includes('safari') && !userAgent.includes('chrome');
        const isFirefox = userAgent.includes('firefox');
        const isEdge = userAgent.includes('edg');

        // If it's a regular browser, return false
        if (isChrome || isSafari || isFirefox || isEdge) {
            return {
                isInWebView: false,
                isIOS: false
            };
        }

        // Check for WebView indicators
        const isAndroidWebView = userAgent.includes('wv');
        const isIOSWebView = /iphone|ipad|ipod/.test(userAgent) &&
            !window.MSStream &&
            !userAgent.includes('safari') &&
            (window.webkit && window.webkit.messageHandlers);

        const isInWebView = isAndroidWebView || isIOSWebView ||
            window.ReactNativeWebView ||
            (window.webkit && window.webkit.messageHandlers);

        return {
            isInWebView,
            isIOS: isIOSWebView
        };
    }

    function openInBrowser(url) {
        const detection = isWebView();

        if (detection.isIOS) {
            // Remove protocol for iOS URL schemes
            const urlWithoutProtocol = url.replace(/^https?:\/\//, '');

            // Try Chrome first
            window.location.href = `x-safari-https://${urlWithoutProtocol}`;

            // Fallback to Safari after a short delay

            // Final fallback to Safari
            setTimeout(() => {
                window.location.href = url;
            }, 200);
        } else {
            // For Android and other platforms
            window.open(url, '_system');
        }
    }

    function showWebViewNotice() {
        const detection = isWebView();
        if (detection.isInWebView) {
            // Show notice and hide register button using jQuery
            $('#webview-notice').show();
            $('#registerButton').hide();

            const currentUrl = window.location.href;
            const browserLink = $('#open-in-browser');

            browserLink.attr('href', currentUrl);
            browserLink.on('click', function (e) {
                e.preventDefault();
                openInBrowser(currentUrl);
            });
        }
    }

    // Run the detection when page loads
    $(document).ready(function () {
        showWebViewNotice();
    });
</script>
<script>
    function validateVietnamesePhone(phone) {
        // Regex cho số điện thoại Việt Nam
        const vietnamesePhoneRegex = /^(0|\+84)(\s?)((3[2-9])|(5[689])|(7[06-9])|(8[1-689])|(9[0-46-9]))(\d)(\s?\d{3})(\s?\d{3})$/;
        return vietnamesePhoneRegex.test(phone);
    }

    $(document).ready(function () {
        $('form').on('submit', function (e) {
            const phoneNumber = $('#phone').val().trim();
            // Xóa thông báo lỗi cũ nếu có
            $('.phone-error').remove();
            @if(isset($webinar->join_settings['phone_required']) && $webinar->join_settings['phone_required'])
            if (!validateVietnamesePhone(phoneNumber)) {
                e.preventDefault();
                $('#phone').after('<div class="text-danger mt-2 phone-error">Vui lòng nhập đúng số điện thoại</div>');
                return false;
            }
            @else
            if (phoneNumber.length > 0) {
                if (!validateVietnamesePhone(phoneNumber)) {
                    e.preventDefault();
                    $('#phone').after('<div class="text-danger mt-2 phone-error">Vui lòng nhập đúng số điện thoại</div>');
                    return false;
                }
            }
            @endif
        });

        // Xóa thông báo lỗi khi người dùng bắt đầu nhập lại
        $('#phone').on('input', function () {
            $('.phone-error').remove();
        });
    });
</script>
</body>
</html>
